/* DiscoverTable.css - Styling to match original DiscoverTable.tsx */

/* Main container */
.discover-table {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: transparent;
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Loading, error, and empty states */
.table-loading,
.table-error,
.table-empty {
  padding: 24px;
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  gap: 16px;
}

.table-loading p,
.table-error p,
.table-empty p {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

.table-error p {
  color: #ff6b6b;
}

/* Table container */
.table-container {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Standard table styling */
.log-table {
  width: 100%;
  border-collapse: collapse;
  background: transparent;
  color: white;
  font-size: 14px;
}

.log-table thead {
  position: sticky;
  top: 0;
  z-index: 10;
  background: rgba(10, 14, 23, 0.8);
  border-bottom: 1px solid rgba(0, 229, 255, 0.2);
}

.log-table th {
  padding: 8px 16px;
  text-align: left;
  font-weight: bold;
  color: white;
  border-bottom: 1px solid rgba(0, 229, 255, 0.2);
  background: rgba(10, 14, 23, 0.8);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.log-table th:hover {
  background: rgba(0, 229, 255, 0.05);
}

/* Table rows */
.log-row {
  border-bottom: 1px solid rgba(0, 229, 255, 0.1);
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.log-row:hover {
  background: rgba(0, 229, 255, 0.02);
}

.log-row.expanded {
  background: rgba(0, 229, 255, 0.05);
}

/* Table cells */
.field-cell {
  padding: 8px 16px;
  color: white;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid rgba(0, 229, 255, 0.1);
}

/* Expand button cell */
.expand-cell {
  width: 40px;
  min-width: 40px;
  padding: 8px;
  text-align: center;
  vertical-align: middle;
}

.expand-button {
  background: rgba(0, 229, 255, 0.1);
  border: 1px solid rgba(0, 229, 255, 0.3);
  color: #00e5ff;
  cursor: pointer;
  font-size: 16px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border-radius: 4px;
  position: relative;
  z-index: 1;
}

.expand-button:hover {
  background: rgba(0, 229, 255, 0.2);
  border-color: rgba(0, 229, 255, 0.5);
  transform: scale(1.1);
}

.expand-button svg {
  pointer-events: none;
  display: block;
}

/* Expanded row details */
.expanded-row {
  background: rgba(0, 0, 0, 0.2);
}

.expanded-row td {
  padding: 0;
  border-bottom: 1px solid rgba(0, 229, 255, 0.1);
}

/* Log detail items */
.log-detail-item {
  display: flex;
  padding: 4px 16px;
  border-bottom: 1px solid rgba(0, 229, 255, 0.05);
}

.log-detail-key {
  color: rgba(255, 255, 255, 0.7);
  font-weight: bold;
  min-width: 150px;
  padding-right: 16px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 4px 8px;
  margin-right: 8px;
}

.log-detail-value {
  color: white;
  word-break: break-word;
  flex: 1;
  padding: 4px 8px;
}

/* Virtualized table container */
.virtualized-table-container {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Expanded rows container for virtualized table */
.expanded-rows-container {
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(0, 229, 255, 0.1);
}

.expanded-row-details {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 229, 255, 0.1);
  color: white;
  font-size: 14px;
}

/* Virtualized table specific styles */
.virtualized-table {
  height: 100%;
  width: 100%;
  background: transparent;
  color: white;
  font-size: 14px;
}

.virtualized-log-table {
  background: transparent;
}

/* Virtualized table header */
.virtualized-table-header {
  display: flex;
  background: rgba(10, 14, 23, 0.8);
  border-bottom: 1px solid rgba(0, 229, 255, 0.2);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-cell {
  padding: 8px;
  font-weight: bold;
  color: white;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  border-right: 1px solid rgba(0, 229, 255, 0.1);
  display: flex;
  align-items: center;
}

.header-cell.expand-column {
  justify-content: center;
  min-width: 40px;
}

.header-cell.field-column {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s ease;
}

.header-cell.field-column:hover {
  background: rgba(0, 229, 255, 0.05);
}

/* Virtualized table rows */
.virtualized-row {
  display: flex;
  border-bottom: 1px solid rgba(0, 229, 255, 0.1);
  cursor: pointer;
  transition: background-color 0.2s ease;
  align-items: center;
}

.virtualized-row:hover {
  background: rgba(0, 229, 255, 0.02);
}

.virtualized-row.expanded {
  background: rgba(0, 229, 255, 0.05);
}

/* Virtualized table cells */
.row-cell {
  padding: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: white;
  border-right: 1px solid rgba(0, 229, 255, 0.05);
}

.row-cell.expand-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
}

.row-cell.field-cell {
  flex: 1;
}

/* Expand button in virtualized table */
.virtualized-row .expand-button {
  background: transparent;
  border: none;
  color: #00e5ff;
  cursor: pointer;
  font-size: 14px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.virtualized-row .expand-button:hover {
  transform: scale(1.2);
  color: rgba(0, 229, 255, 1);
}

/* Level field special styling - matching original colors exactly */
.field-cell .level-info,
.row-cell .level-info {
  color: #00bfff; /* Light blue - matching original */
  font-weight: normal; /* Ensure no bold inheritance */
}

.field-cell .level-warning,
.row-cell .level-warning {
  color: #ffa500; /* Orange - matching original */
  font-weight: normal; /* Ensure no bold inheritance */
}

.field-cell .level-error,
.row-cell .level-error {
  color: #ff4500; /* Red-orange - matching original */
  font-weight: normal; /* Ensure no bold inheritance */
}

.field-cell .level-critical,
.row-cell .level-critical {
  color: #ff0000; /* Red - matching original */
  font-weight: normal; /* Ensure no bold inheritance */
}

.field-cell .level-debug,
.row-cell .level-debug {
  color: #66bb6a; /* Green - keeping this as it wasn't in original but looks good */
  font-weight: normal; /* Ensure no bold inheritance */
}

/* Pagination controls styling */
.table-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: rgba(10, 14, 23, 0.8);
  border-top: 1px solid rgba(0, 229, 255, 0.2);
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
}

.pagination-info {
  color: rgba(255, 255, 255, 0.7);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid rgba(0, 229, 255, 0.3);
  background: rgba(10, 14, 23, 0.8);
  color: rgba(0, 229, 255, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  padding: 4px 8px;
  min-width: auto;
}

.pagination-button:hover:not(:disabled) {
  background: rgba(0, 229, 255, 0.1);
  border-color: rgba(0, 229, 255, 0.5);
  color: rgba(0, 229, 255, 1);
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(10, 14, 23, 0.4);
  border-color: rgba(0, 229, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
}

.pagination-pages {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  padding: 0 8px;
}

/* Page size selector styling */
.page-size-select {
  padding: 6px 28px 6px 10px;
  border-radius: 4px;
  border: 1px solid rgba(0, 229, 255, 0.3);
  background: rgba(10, 14, 23, 0.8);
  color: rgba(0, 229, 255, 0.8);
  font-size: 14px;
  font-family: monospace;
  cursor: pointer;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%2300e5ff%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 10px auto;
  transition: all 0.2s ease;
}

.page-size-select:hover {
  border-color: rgba(0, 229, 255, 0.5);
}

.page-size-select:focus {
  border-color: rgba(0, 229, 255, 0.5);
  box-shadow: 0 0 0 2px rgba(0, 229, 255, 0.2);
}

.page-size-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-size-select option {
  padding: 6px 10px;
  background-color: rgba(10, 14, 23, 0.95);
  color: rgba(255, 255, 255, 0.8);
}

.page-size-select option:checked {
  background-color: rgba(0, 229, 255, 0.1);
  color: rgba(0, 229, 255, 0.9);
}
