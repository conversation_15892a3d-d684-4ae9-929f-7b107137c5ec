import React, { useCallback, useMemo } from 'react';
import { FixedSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import { LogEntry } from '../../types/discover';
import './DiscoverTable.css';

interface VirtualizedTableProps {
  data: LogEntry[];
  selectedFields: string[];
  formatFieldValue: (value: unknown, fieldName: string) => string;
  getFieldValue: (log: LogEntry, fieldPath: string) => unknown;
  onRowExpand?: (logId: string) => void;
  expandedRows?: Record<string, boolean>;
  className?: string;
  rowHeight?: number;
  headerHeight?: number;
}

/**
 * A virtualized table component for efficiently rendering large datasets
 */
const VirtualizedTable: React.FC<VirtualizedTableProps> = ({
  data,
  selectedFields,
  formatFieldValue,
  getFieldValue,
  onRowExpand,
  expandedRows = {},
  className = '',
  rowHeight = 40,
  headerHeight = 40
}) => {
  // Memoize field widths calculation
  const fieldWidths = useMemo(() => {
    const totalWidth = 100;
    const expandButtonWidth = onRowExpand ? 5 : 0;
    const availableWidth = totalWidth - expandButtonWidth;
    const fieldWidth = availableWidth / Math.max(selectedFields.length, 1);
    
    return {
      expandButton: expandButtonWidth,
      fields: selectedFields.map(() => fieldWidth)
    };
  }, [selectedFields, onRowExpand]);
  
  // Render table header
  const TableHeader = useCallback(() => {
    return (
      <div
        className="virtualized-table-header"
        style={{ height: `${headerHeight}px` }}
      >
        {onRowExpand && (
          <div
            className="header-cell expand-column"
            style={{ width: `${fieldWidths.expandButton}%` }}
          ></div>
        )}

        {selectedFields.map((field, index) => (
          <div
            key={field}
            className="header-cell field-column"
            style={{ width: `${fieldWidths.fields[index]}%` }}
          >
            {field}
          </div>
        ))}
      </div>
    );
  }, [selectedFields, fieldWidths, headerHeight, onRowExpand]);
  
  // Get level color function (matching DiscoverTable)
  const getLevelColor = useCallback((level: string): string => {
    switch (level?.toLowerCase()) {
      case 'info':
        return '#42a5f5'; // Blue
      case 'warning':
        return '#ffa726'; // Orange
      case 'error':
        return '#ff6b6b'; // Red
      case 'debug':
        return '#66bb6a'; // Green
      case 'critical':
        return '#ff6b6b'; // Red (same as error)
      default:
        return '#42a5f5'; // Default to blue
    }
  }, []);

  // Row renderer for virtualized list
  const Row = useCallback(({ index, style }: { index: number; style: React.CSSProperties }) => {
    const log = data[index];
    if (!log) return null;

    return (
      <div
        className={`virtualized-row ${expandedRows[log.id] ? 'expanded' : ''}`}
        style={{ ...style }}
      >
        {onRowExpand && (
          <div
            className="row-cell expand-cell"
            style={{ width: `${fieldWidths.expandButton}%` }}
          >
            <button
              onClick={() => onRowExpand(log.id)}
              className="expand-button"
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="#00e5ff"
                strokeWidth="2"
                style={{
                  transform: expandedRows[log.id] ? 'rotate(90deg)' : 'rotate(0deg)',
                  transition: 'transform 0.2s',
                }}
              >
                <polyline points="9 18 15 12 9 6" />
              </svg>
            </button>
          </div>
        )}

        {selectedFields.map((field, fieldIndex) => {
          const value = getFieldValue(log, field);
          const formattedValue = formatFieldValue(value, field);

          // Special styling for level field
          if (field === 'level') {
            const levelClass = `level-${(value as string)?.toLowerCase() || 'info'}`;
            return (
              <div
                key={`${log.id}-${field}`}
                className="row-cell field-cell"
                style={{ width: `${fieldWidths.fields[fieldIndex]}%` }}
              >
                <span className={levelClass}>
                  {formattedValue}
                </span>
              </div>
            );
          }

          return (
            <div
              key={`${log.id}-${field}`}
              className="row-cell field-cell"
              style={{ width: `${fieldWidths.fields[fieldIndex]}%` }}
            >
              {formattedValue}
            </div>
          );
        })}
      </div>
    );
  }, [data, selectedFields, fieldWidths, expandedRows, onRowExpand, formatFieldValue, getFieldValue, getLevelColor]);
  
  return (
    <div className={`virtualized-table ${className}`} style={{ height: '100%', width: '100%' }}>
      <TableHeader />
      
      <div style={{ height: `calc(100% - ${headerHeight}px)` }}>
        <AutoSizer>
          {({ height, width }) => (
            <List
              height={height}
              itemCount={data.length}
              itemSize={rowHeight}
              width={width}
            >
              {Row}
            </List>
          )}
        </AutoSizer>
      </div>
    </div>
  );
};

export default React.memo(VirtualizedTable);